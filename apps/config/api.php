<?php
$router->setDefaultModule('frontend');

//AUTH API
$router->add('/api/auth/login', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'login',
])->via(['POST']);

$router->add('/api/auth/register', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'register',
])->via(['POST']);

$router->add('/api/auth/login-facebook', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'loginFacebook',
])->via(['POST']);

$router->add('/api/auth/login-google', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'loginGoogle',
])->via(['POST']);

$router->add('/api/auth/login-apple', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'loginApple',
])->via(['POST']);

$router->add('/api/auth/send-otp', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'sendOtp',
])->via(['POST']);

$router->add('/api/auth/verify-otp', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'verifyOtp',
])->via(['POST']);

$router->add('/api/auth/verify-otp-contact', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'verifyOtpContact',
])->via(['POST']);

$router->add('/api/auth/delete-account', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'deleteAccount',
])->via(['POST']);

$router->add('/api/auth/change-password', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'changePassword',
])->via(['POST']);

$router->add('/api/auth/forgot-password', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'forgotPassword',
])->via(['POST']);

$router->add('/api/auth/verify-otp-password',[
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'verifyOtpPassword',
])->via(['POST']);

$router->add('/api/auth/reset-password', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'resetPassword',
])->via(['POST']);

$router->add('/api/auth/renew-token', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'auth-api',
  'action'     => 'renewToken',
])->via(['POST']);

//USER API
$router->add('/api/user/info', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'user-api',
  'action'     => 'getUserInfo',
])->via(['GET']);

$router->add('/api/user/update', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'user-api',
  'action'     => 'updateUser',
])->via(['POST']);

$router->add('/api/user/wallet', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'user-api',
  'action'     => 'getUserWallet',
])->via(['GET']);

$router->add('/api/transaction/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'user-api',
  'action'     => 'transactionList',
])->via(['GET']);

//LOCATION API
$router->add('/api/location/provinces', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'location-api',
  'action'     => 'getProvinces',
])->via(['GET']);

$router->add('/api/location/districts/{province_code}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'location-api',
  'action'     => 'getDistricts',
])->via(['GET']);

$router->add('/api/location/wards/{district_code}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'location-api',
  'action'     => 'getWards',
])->via(['GET']);

$router->add('/api/location/streets/{district_code}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'location-api',
  'action'     => 'getStreets',
])->via(['GET']);

//HOSTEL API
$router->add('/api/hostel/type', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'type',
])->via(['GET']);

$router->add('/api/hostel/props', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'getProps',
])->via(['GET']);

$router->add('/api/hostel/add', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'add',
])->via(['POST']);

$router->add('/api/hostel/{id:[0-9]+}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'show',
])->via(['GET']);

$router->add('/api/hostel/{id:[0-9]+}/update', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'update',
])->via(['POST']);

$router->add('/api/hostel/{id}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'delete',
])->via(['DELETE']);

$router->add('/api/hostel/toggle-status/{id}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'toggleStatus',
])->via(['PUT']);

$router->add('/api/hostel/room/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'room-api',
  'action'     => 'list',
])->via(['GET']);

$router->add('/api/hostel/room/add', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'room-api',
  'action'     => 'add',
])->via(['POST']);

$router->add('/api/hostel/room/{id:[0-9]+}/update', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'room-api',
  'action'     => 'update',
])->via(['POST']);

$router->add('/api/hostel/room/{id:[0-9]+}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'room-api',
  'action'     => 'edit',
])->via(['GET']);

$router->add('/api/hostel/room/{id:[0-9]+}/delete', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'room-api',
  'action'     => 'delete',
])->via(['DELETE']);

$router->add('/api/hostel/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'list',
])->via(['GET']);

$router->add('/api/hostel/detail/{id:[0-9]+}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'detail',
])->via(['GET']);

// $router->add('/api/hostel/room/{id}', [
//   'namespace'  => 'Modules\Frontend\Controllers\Api',
//   'controller' => 'hostel-api',
//   'action'     => 'rooms',
// ])->via(['GET']);

$router->add('/api/hostel/dashboard', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'dashboard',
])->via(['GET']);

$router->add('/api/hostel/statistic/views', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'statisticViews',
])->via(['GET']);

//HOST API
$router->add('/api/host/contact', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'host-api',
  'action'     => 'hostContact',
])->via(['GET']);

$router->add('/api/host/send-otp', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'host-api',
  'action'     => 'sendOtp',
])->via(['POST']);

$router->add('/api/host/verify-otp', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'host-api',
  'action'     => 'verifyOtp',
])->via(['POST']);

//PAYMENT API
$router->add('/api/payment/recharge', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'payment-api',
  'action'     => 'recharge',
])->via(['POST']);

$router->add('/api/payment/discount', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'payment-api',
  'action'     => 'discount',
])->via(['GET']);

$router->add('/api/payment/banks', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'payment-api',
  'action'     => 'banks',
])->via(['GET']);

$router->add('/api/payment/{orderId}/has-paid', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'payment-api',
  'action'     => 'hasPaid',
])->via(['GET']);

//UPGRADE API
$router->add('/api/hostel/premium-package/{code}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'premiumPackage',
])->via(['GET']);

$router->add('/api/hostel/upgrade-premium', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'upgradePremium',
])->via(['POST']);

//PUSH HOSTEL API
$router->add('/api/hostel/push', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'hostel-api',
  'action'     => 'pushHostel',
])->via(['POST']);

//SLOT API
$router->add('/api/host/slot-package', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'host-api',
  'action'     => 'slots',
])->via(['GET']);

$router->add('/api/host/buy-slot', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'host-api',
  'action'     => 'buySlot',
])->via(['POST']);

$router->add('/api/host/slot', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'host-api',
  'action'     => 'slot',
])->via(['GET']);

// MEMBERSHIP API
$router->add('/api/membership/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'membership-api',
  'action'     => 'list',
])->via(['GET']);

$router->add('/api/membership/buy', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'membership-api',
  'action'     => 'buy',
])->via(['POST']);

$router->add('/api/membership/contact', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'membership-api',
  'action'     => 'contact',
])->via(['POST']);

$router->add('/api/membership/upgrade-hot', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'membership-api',
  'action'     => 'upgradeHot',
])->via(['POST']);

$router->add('/api/membership/push', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'membership-api',
  'action'     => 'push',
])->via(['POST']);

$router->add('/api/rule/dieu-khoan-cam-ket', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'rule-api',
  'action'     => 'dieuKhoanCamKet',
])->via(['GET']);

$router->add('/api/rule/chinh-sach-bao-mat', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'rule-api',
  'action'     => 'chinhSachBaoMat',
])->via(['GET']);


$router->add('/api/event/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'event-api',
  'action'     => 'list',
])->via(['GET']);

$router->add('/api/notification/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'notification-api',
  'action'     => 'list',
])->via(['GET']);


$router->add('/api/store/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'store-api',
  'action'     => 'list',
])->via(['GET']);

//AD HOSTEL API
$router->add('/api/ad-hostel/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'ad-hostel-api',
  'action'     => 'list',
])->via(['GET']);

$router->add('/api/ad-hostel/{id:[0-9]+}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'ad-hostel-api',
  'action'     => 'detail',
])->via(['GET']);

$router->add('/api/ad-hostel/add', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'ad-hostel-api',
  'action'     => 'add',
])->via(['POST']);

$router->add('/api/ad-hostel/{id:[0-9]+}/update', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'ad-hostel-api',
  'action'     => 'update',
])->via(['POST']);

$router->add('/api/ad-hostel/{id:[0-9]+}/toggle-publish', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'ad-hostel-api',
  'action'     => 'togglePublish',
])->via(['POST']);

$router->add('/api/ad-hostel/{id:[0-9]+}/upgrade', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'ad-hostel-api',
  'action'     => 'upgrade',
])->via(['POST']);

$router->add('/api/ad-hostel/upgrade-packages', [
  'namespace'  => 'Modules\Frontend\Controllers\Api',
  'controller' => 'ad-hostel-api',
  'action'     => 'upgradePackages',
])->via(['GET']);


