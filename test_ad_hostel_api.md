# Test API Endpoints cho AdHostelApiController

## 1. API lấy danh sách gói nâng cấp

### Endpoint
```
GET /api/ad-hostel/upgrade-packages
```

### Headers
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

### Query Parameters
- `type` (optional): Loại gói (`hot`, `push`, `slot`, `all` - default)

### Test Cases

#### Test 1: <PERSON><PERSON><PERSON> t<PERSON>t cả gói nâng cấp
```bash
curl -X GET "http://localhost/api/ad-hostel/upgrade-packages" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

#### Test 2: L<PERSON>y gói HOT
```bash
curl -X GET "http://localhost/api/ad-hostel/upgrade-packages?type=hot" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

#### Test 3: <PERSON><PERSON><PERSON> gói PUSH
```bash
curl -X GET "http://localhost/api/ad-hostel/upgrade-packages?type=push" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

#### Test 4: Lấy gói SLOT
```bash
curl -X GET "http://localhost/api/ad-hostel/upgrade-packages?type=slot" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### Expected Response
```json
{
  "status": true,
  "message": "Danh sách gói nâng cấp",
  "data": {
    "hot": [
      {
        "id": 1,
        "type": "hot",
        "name": "Gói HOT 7 ngày",
        "description": "Tin HOT hiển thị ưu tiên trong 7 ngày",
        "short_description": "Tin HOT 7 ngày",
        "price": 50000,
        "quantity": 1,
        "day": 7,
        "status": 1
      }
    ],
    "push": [...],
    "slot": [...]
  }
}
```

## 2. API nâng cấp gói dịch vụ

### Endpoint
```
POST /api/ad-hostel/{id}/upgrade
```

### Headers
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

### Request Body
```json
{
  "package_id": 1
}
```

### Test Cases

#### Test 1: Nâng cấp thành công
```bash
curl -X POST "http://localhost/api/ad-hostel/1/upgrade" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "package_id": 1
  }'
```

#### Test 2: Không đủ số dư
```bash
curl -X POST "http://localhost/api/ad-hostel/1/upgrade" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "package_id": 999
  }'
```

#### Test 3: Không có quyền
```bash
curl -X POST "http://localhost/api/ad-hostel/999/upgrade" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "package_id": 1
  }'
```

### Expected Response Success
```json
{
  "status": true,
  "message": "Nâng cấp thành công",
  "data": {
    "ad_hostel_id": 1,
    "package_type": "hot",
    "package_name": "Gói HOT 7 ngày",
    "expired_date": "2024-01-15 23:59:59"
  }
}
```

### Expected Response Error
```json
{
  "status": false,
  "message": "Số dư ví không đủ, vui lòng nạp tiền",
  "data": []
}
```

## 3. Validation Tests

### Test Authentication
```bash
# Test without token
curl -X GET "http://localhost/api/ad-hostel/upgrade-packages"

# Test with invalid token
curl -X GET "http://localhost/api/ad-hostel/upgrade-packages" \
  -H "Authorization: Bearer invalid_token"
```

### Test Authorization
```bash
# Test with user không phải host
curl -X GET "http://localhost/api/ad-hostel/upgrade-packages" \
  -H "Authorization: Bearer USER_WITHOUT_HOST_TOKEN"
```

## 4. Performance Tests

### Load Test
```bash
# Test với nhiều request đồng thời
for i in {1..10}; do
  curl -X GET "http://localhost/api/ad-hostel/upgrade-packages" \
    -H "Authorization: Bearer YOUR_TOKEN" &
done
wait
```

## 5. Integration Tests

### Test Flow hoàn chỉnh
1. Lấy danh sách packages
2. Chọn package phù hợp
3. Thực hiện upgrade
4. Kiểm tra kết quả

```bash
# Step 1: Get packages
PACKAGES=$(curl -s -X GET "http://localhost/api/ad-hostel/upgrade-packages" \
  -H "Authorization: Bearer YOUR_TOKEN")

# Step 2: Extract package ID
PACKAGE_ID=$(echo $PACKAGES | jq '.data.hot[0].id')

# Step 3: Upgrade
curl -X POST "http://localhost/api/ad-hostel/1/upgrade" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{\"package_id\": $PACKAGE_ID}"
```
